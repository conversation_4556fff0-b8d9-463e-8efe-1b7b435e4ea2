import { WalletScreenNames } from '@bp/bppay-wallet-feature';
import { useCharge } from '@bp/charge-mfe';
import Notifications from '@components/Notifications/Notifications';
import { useConfig } from '@providers/ConfigProvider';
import { useMigration } from '@providers/MigrationContextProvider';
import { useReturnNavigation } from '@providers/ReturnNavigationProvider';
import { createStackNavigator } from '@react-navigation/stack';
import OutageBottomSheet from '@screens/Outage/OutageBottomSheet/OutageBottomSheet';
import PaymentDelayedScreen from '@screens/PaymentDelayed/PaymentDelayed';
import UberProDisconnectScreen from '@screens/UberPro/UberProDisconnect';
import UberProLinkExpiredScreen from '@screens/UberPro/UberProLinkExpired';
import React, { useEffect } from 'react';
import { StatusBar } from 'react-native';

import ChargeActivity from './ChargeActivity/ChargeActivity';
import CompleteConsents from './CompleteSetUp/CompleteConsents';
import CompleteSetUp from './CompleteSetUp/CompleteSetUp';
import Credit from './Credit/Credit';
import { DataPrivacyNotice } from './DataPrivacyNotice/DataPrivacyNotice';
import Error from './Error/Error';
import ChangeLocation from './Help/ChangeLocation';
import { MigrationJourney } from './MigrationJourney';
import Offers from './Offers/Offers';
import PartnerDriver from './PartnerDriver/PartnerDriver';
import Registration from './Registration/Registration';
import RFID from './RFID/RFID';
import { RfidError } from './RfidError/RfidError';
import RTBF from './RTBF/RTBF';
import Subscription from './Subscription/Subscription';
import { RegistrationMiddleware } from '../middleware/RegistrationMiddleware';
import Tabs from './Tabs/Tabs';
import IneligibleAccount from './UberPro/IneligibleAccount/IneligibleAccount';
import SuccessfullyVerified from './UberPro/SuccessfullyVerified/SuccessfullyVerified';
import UberProUnlinkedSuccessfully from './UberPro/UberProUnlinkedSuccessfully';
import Wallet from './Wallet/Wallet';
import WelcomeScreen from './Welcome/WelcomeScreen';

// Initialise bottom navigator
const Stack = createStackNavigator();

// Setting Tabs as default export for now, this allows a stack navigator or additional tab stack
// to be added in the future
export default () => {
  const { profile_mfe } = useConfig();
  const { isMigrating } = useMigration();
  const { isCharging } = useCharge();
  const { returnParams, returnNavigate } = useReturnNavigation();

  useEffect(() => {
    if (!isMigrating && returnParams) {
      returnNavigate();
    }
  }, [isMigrating, returnNavigate, returnParams]);

  // Return migration journey navigator if migrating
  if (isMigrating && !isCharging) {
    return <MigrationJourney />;
  }

  // Return main app navigator if not migrating
  return (
    <>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#ffffff"
        translucent
      />
      <RegistrationMiddleware />
      <Stack.Navigator
        initialRouteName="Tabs"
        screenOptions={{
          cardStyle: { backgroundColor: '#ffffff' },
        }}>
        <Stack.Screen
          name="Tabs"
          component={Tabs}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="Registration"
          component={Registration}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="ChargeActivity"
          component={ChargeActivity}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="RFID"
          component={RFID}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={WalletScreenNames.HostRoot}
          component={Wallet}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'RTBF'}
          component={RTBF}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'Subscription'}
          component={Subscription}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'Credit'}
          component={Credit}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'PartnerDriver'}
          component={PartnerDriver}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'CompleteSetUp'}
          component={CompleteSetUp}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'CompleteConsents'}
          component={CompleteConsents}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'Error'}
          component={Error}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'rfidError'}
          component={RfidError}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'DataPrivacyNotice'}
          component={DataPrivacyNotice}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'Welcome'}
          component={WelcomeScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'ChangeLocation'}
          component={ChangeLocation}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'UberProLinkExpired'}
          component={UberProLinkExpiredScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name={'UberProDisconnect'}
          component={UberProDisconnectScreen}
          options={{ headerShown: false }}
        />

        <Stack.Screen
          name={'UberProUnlinkedSuccessfully'}
          component={UberProUnlinkedSuccessfully}
          options={{ headerShown: false }}
        />
        {profile_mfe.offers && (
          <Stack.Screen
            name={'Offers'}
            component={Offers}
            options={{
              headerShown: false,
            }}
          />
        )}
        <Stack.Screen
          name={'PaymentDelayed'}
          component={PaymentDelayedScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'IneligibleAccount'}
          component={IneligibleAccount}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={'SuccessfullyVerified'}
          component={SuccessfullyVerified}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>

      <OutageBottomSheet />
      <Notifications />
    </>
  );
};
