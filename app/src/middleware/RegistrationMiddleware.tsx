import { useAuth } from '@bp/pulse-auth-sdk';
import { CipStartJourneyFlow } from '@bp/pulse-auth-sdk/dist/types';
import { useLanguage } from '@bp/profile-mfe';
import { logger } from '@utils/logger';
import { useCallback, useEffect } from 'react';
import { Linking } from 'react-native';

export type LinkingEvent = { url: string } | string | null;

export const RegistrationMiddleware = () => {
  const { loginOrRegister } = useAuth();
  const { language } = useLanguage();

  const handleLinking = useCallback((event: LinkingEvent) => {
    const url = event && typeof event === 'object' ? event.url : event;
    if (url?.includes('create_account')) {
      loginOrRegister(language, CipStartJourneyFlow.REGISTRATION);
    }
  }, [language, loginOrRegister]);

  useEffect(() => {
    Linking.getInitialURL()
      .then(handleLinking)
      .catch(err => logger.error('Failed to get initial URL', err));

    const handleLink = Linking.addEventListener('url', handleLinking);
    return () => handleLink.remove();
  }, [handleLinking]);

  return null;
};
